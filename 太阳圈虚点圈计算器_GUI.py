#!/usr/bin/env python3
"""
太阳圈虚点圈射线交点计算器 - GUI版本
使用JPL官方标准轨道参数
调用太阳圈虚点圈交点计算.py进行计算
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import os
import subprocess
import sys

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class SunVirtualCircleCalculator:
    def __init__(self, root):
        self.root = root
        self.root.title("太阳圈虚点圈射线交点计算器")
        self.root.geometry("1200x800")
        
        # 数据存储
        self.df = None
        self.orbital_params = None
        self.virtual_params = None
        self.intersections_df = None
        
        # JPL官方标准轨道参数
        self.standard_params = {
            'Earth': {
                'name': '地球',
                'semi_major_axis_au': 1.00000261,
                'eccentricity': 0.01671123,
                'color': 'blue'
            },
            'Mars': {
                'name': '火星',
                'semi_major_axis_au': 1.52371034,
                'eccentricity': 0.09339410,
                'color': 'red'
            },
            'Venus': {
                'name': '金星',
                'semi_major_axis_au': 0.72333566,
                'eccentricity': 0.00677672,
                'color': 'orange'
            },
            'Mercury': {
                'name': '水星',
                'semi_major_axis_au': 0.38709927,
                'eccentricity': 0.20563593,
                'color': 'gray'
            },
            'Jupiter': {
                'name': '木星',
                'semi_major_axis_au': 5.20288700,
                'eccentricity': 0.04838624,
                'color': 'brown'
            },
            'Saturn': {
                'name': '土星',
                'semi_major_axis_au': 9.53667594,
                'eccentricity': 0.05386179,
                'color': 'gold'
            }
        }
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 1. 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="1. 选择星历表CSV文件", padding="5")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        file_frame.columnconfigure(1, weight=1)
        
        ttk.Button(file_frame, text="选择文件", command=self.select_file).grid(row=0, column=0, padx=5)
        self.file_label = ttk.Label(file_frame, text="未选择文件")
        self.file_label.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5)
        
        # 2. 行星选择区域
        planet_frame = ttk.LabelFrame(main_frame, text="2. 选择行星", padding="5")
        planet_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        self.planet_var = tk.StringVar(value="Earth")
        planet_combo = ttk.Combobox(planet_frame, textvariable=self.planet_var, 
                                   values=list(self.standard_params.keys()),
                                   state="readonly", width=15)
        planet_combo.grid(row=0, column=0, padx=5)
        planet_combo.bind('<<ComboboxSelected>>', self.on_planet_changed)
        
        # 轨道参数调整
        params_frame = ttk.Frame(planet_frame)
        params_frame.grid(row=0, column=1, padx=20)
        
        ttk.Label(params_frame, text="半长轴 a (AU):").grid(row=0, column=0, padx=5)
        self.a_var = tk.StringVar(value="1.00000261")
        self.a_entry = ttk.Entry(params_frame, textvariable=self.a_var, width=12)
        self.a_entry.grid(row=0, column=1, padx=5)
        
        ttk.Label(params_frame, text="偏心率 e:").grid(row=0, column=2, padx=5)
        self.e_var = tk.StringVar(value="0.01671123")
        self.e_entry = ttk.Entry(params_frame, textvariable=self.e_var, width=12)
        self.e_entry.grid(row=0, column=3, padx=5)
        
        # 3. 公式显示区域
        formula_frame = ttk.LabelFrame(main_frame, text="3. 虚点公式", padding="5")
        formula_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        formula_text = """太阳圈半径公式：a(1-e²)/2
虚点值公式：2ae
虚点圈半径公式：2a - a(1-e²)/2
虚点坐标：(0, -2ae, 0)
太阳圈：在XY平面内，圆心为太阳，半径为太阳圈半径
虚点圈：在XY平面内，圆心为虚点，半径为虚点圈半径"""
        
        ttk.Label(formula_frame, text=formula_text, justify=tk.LEFT).grid(row=0, column=0, sticky=tk.W)
        
        # 4. 计算按钮
        ttk.Button(main_frame, text="开始计算", command=self.calculate).grid(row=3, column=0, columnspan=2, pady=10)
        
        # 5. 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="4. 计算结果", padding="5")
        result_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)
        
        # 创建Notebook用于多标签页
        self.notebook = ttk.Notebook(result_frame)
        self.notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 结果文本标签页
        self.result_text = scrolledtext.ScrolledText(self.notebook, height=15, width=60)
        self.notebook.add(self.result_text, text="计算结果")
        
        # 图表标签页
        self.chart_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.chart_frame, text="可视化图表")
        
        # 数据表格标签页
        self.table_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.table_frame, text="交点数据表")
        
    def select_file(self):
        """选择CSV文件"""
        file_path = filedialog.askopenfilename(
            title="选择星历表CSV文件",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        
        if file_path:
            try:
                self.df = pd.read_csv(file_path)
                self.file_label.config(text=f"已选择: {os.path.basename(file_path)}")
                self.result_text.insert(tk.END, f"[成功] 成功加载文件: {os.path.basename(file_path)}\n")
                self.result_text.insert(tk.END, f"  数据点数量: {len(self.df)}\n")
                self.result_text.insert(tk.END, f"  列名: {list(self.df.columns)}\n\n")
                self.result_text.see(tk.END)
            except Exception as e:
                messagebox.showerror("错误", f"加载文件失败: {e}")
                
    def on_planet_changed(self, event=None):
        """行星选择改变时更新参数"""
        planet = self.planet_var.get()
        if planet in self.standard_params:
            params = self.standard_params[planet]
            self.a_var.set(str(params['semi_major_axis_au']))
            self.e_var.set(str(params['eccentricity']))
            
    def get_orbital_parameters(self):
        """获取轨道参数"""
        try:
            a = float(self.a_var.get())
            e = float(self.e_var.get())
            
            # 计算其他参数
            b = a * np.sqrt(1 - e**2)
            r_min = a * (1 - e)  # 近日点
            r_max = a * (1 + e)  # 远日点
            
            planet_name = self.standard_params[self.planet_var.get()]['name']
            
            return {
                'planet_name': planet_name,
                'a': a,
                'e': e,
                'b': b,
                'r_min': r_min,
                'r_max': r_max
            }
        except ValueError:
            messagebox.showerror("错误", "请输入有效的数值")
            return None
            
    def calculate_virtual_parameters(self, orbital_params):
        """计算虚点参数"""
        a = orbital_params['a']
        e = orbital_params['e']
        
        # 虚点值：2ae
        virtual_point_value = 2 * a * e
        
        # 太阳圈半径：a(1-e²)/2
        sun_circle_radius = a * (1 - e**2) / 2

        # 虚点圈半径：2a - a(1-e²)/2
        virtual_radius = 2 * a - sun_circle_radius
        
        # 虚点坐标：(0, -2ae, 0)
        virtual_point = np.array([0, -virtual_point_value, 0])
        
        return {
            'virtual_point_value': virtual_point_value,
            'virtual_radius': virtual_radius,
            'virtual_point': virtual_point
        }

    def calculate_ray_circle_intersection(self, ray_origin, ray_direction, circle_center, circle_radius):
        """计算射线与圆的交点（在XY平面内）"""

        # 确保在XY平面内计算（Z=0）
        ray_origin_2d = np.array([ray_origin[0], ray_origin[1]])
        ray_direction_2d = np.array([ray_direction[0], ray_direction[1]])
        circle_center_2d = np.array([circle_center[0], circle_center[1]])

        # 标准化射线方向
        ray_direction_2d = ray_direction_2d / np.linalg.norm(ray_direction_2d)

        # 设 oc = ray_origin_2d - circle_center_2d
        oc = ray_origin_2d - circle_center_2d

        # 展开得到二次方程: a*t² + b*t + c = 0
        a = np.dot(ray_direction_2d, ray_direction_2d)  # 应该等于1（已标准化）
        b = 2.0 * np.dot(oc, ray_direction_2d)
        c = np.dot(oc, oc) - circle_radius**2

        # 计算判别式
        discriminant = b**2 - 4*a*c

        if discriminant < 0:
            return []
        elif discriminant == 0:
            t = -b / (2*a)
            if t >= 0:
                intersection_2d = ray_origin_2d + t * ray_direction_2d
                intersection_3d = np.array([intersection_2d[0], intersection_2d[1], 0])
                return [intersection_3d]
            else:
                return []
        else:
            sqrt_discriminant = np.sqrt(discriminant)
            t1 = (-b - sqrt_discriminant) / (2*a)
            t2 = (-b + sqrt_discriminant) / (2*a)

            intersections = []

            if t1 >= 0:
                intersection_2d = ray_origin_2d + t1 * ray_direction_2d
                intersection_3d = np.array([intersection_2d[0], intersection_2d[1], 0])
                intersections.append(intersection_3d)

            if t2 >= 0:
                intersection_2d = ray_origin_2d + t2 * ray_direction_2d
                intersection_3d = np.array([intersection_2d[0], intersection_2d[1], 0])
                intersections.append(intersection_3d)

            return intersections

    def calculate_intersections(self):
        """计算所有射线与虚点圆的交点"""

        if self.df is None:
            return None

        virtual_point = self.virtual_params['virtual_point']
        virtual_radius = self.virtual_params['virtual_radius']

        # 太阳位置（原点）
        sun_position = np.array([0, 0, 0])

        intersections_data = []

        for i, row in self.df.iterrows():
            # 地球位置
            earth_position = np.array([
                row['x_position_au'],
                row['y_position_au'],
                row['z_position_au']
            ])

            # 射线方向：从太阳指向地球的XY坐标
            ray_direction = np.array([earth_position[0], earth_position[1], 0])

            # 如果射线方向为零向量，跳过
            if np.linalg.norm(ray_direction) < 1e-10:
                intersections = []
            else:
                intersections = self.calculate_ray_circle_intersection(
                    sun_position, ray_direction, virtual_point, virtual_radius
                )

            # 记录结果
            date = row.get('date', f'Day {i+1}')
            julian_date = row.get('julian_date', 0)

            intersection_record = {
                '天数': i + 1,
                '日期': date,
                '儒略日': julian_date,
                '地球X坐标_AU': earth_position[0],
                '地球Y坐标_AU': earth_position[1],
                '地球Z坐标_AU': earth_position[2],
                '射线方向X': ray_direction[0],
                '射线方向Y': ray_direction[1],
                '射线方向Z': ray_direction[2],
                '交点数量': len(intersections)
            }

            # 添加交点坐标
            for j, intersection in enumerate(intersections):
                intersection_record[f'交点{j+1}_X坐标_AU'] = intersection[0]
                intersection_record[f'交点{j+1}_Y坐标_AU'] = intersection[1]
                intersection_record[f'交点{j+1}_Z坐标_AU'] = intersection[2]

            intersections_data.append(intersection_record)

        return pd.DataFrame(intersections_data)

    def calculate(self):
        """执行计算 - 调用外部计算脚本"""

        if self.df is None:
            messagebox.showerror("错误", "请先选择星历表文件")
            return

        # 清空结果显示
        self.result_text.delete(1.0, tk.END)

        # 获取轨道参数
        self.orbital_params = self.get_orbital_parameters()
        if self.orbital_params is None:
            return

        # 计算虚点参数
        self.virtual_params = self.calculate_virtual_parameters(self.orbital_params)

        # 显示参数
        self.display_parameters()

        # 调用外部计算脚本
        self.result_text.insert(tk.END, "正在调用太阳圈虚点圈交点计算程序...\n")
        self.result_text.see(tk.END)
        self.root.update()

        try:
            # 运行外部计算脚本，设置正确的编码
            result = subprocess.run([
                sys.executable, "太阳圈虚点圈交点计算.py"
            ], capture_output=True, text=True, cwd=os.getcwd(),
            encoding='utf-8', errors='replace')

            if result.returncode == 0:
                self.result_text.insert(tk.END, "[成功] 计算完成！\n")
                self.result_text.insert(tk.END, result.stdout)

                # 加载计算结果
                self.load_calculation_results()

            else:
                self.result_text.insert(tk.END, f"[错误] 计算失败:\n{result.stderr}\n")

        except Exception as e:
            self.result_text.insert(tk.END, f"[错误] 调用计算程序失败: {e}\n")

        self.result_text.see(tk.END)

    def load_calculation_results(self):
        """加载外部计算程序的结果"""
        try:
            # 加载计算结果CSV文件
            csv_file = "planet_data/太阳圈虚点圈交点数据.csv"
            if os.path.exists(csv_file):
                # 跳过注释行，读取数据
                self.intersections_df = pd.read_csv(csv_file, comment='#')
                self.result_text.insert(tk.END, f"[成功] 成功加载交点数据: {len(self.intersections_df)} 行\n")

                # 创建可视化和数据表
                self.create_visualization_from_results()
                self.create_data_table_from_results()

            else:
                self.result_text.insert(tk.END, f"[错误] 未找到结果文件: {csv_file}\n")

        except Exception as e:
            self.result_text.insert(tk.END, f"[错误] 加载结果失败: {e}\n")

    def create_visualization_from_results(self):
        """基于计算结果创建可视化图表"""

        # 清空之前的图表
        for widget in self.chart_frame.winfo_children():
            widget.destroy()

        # 创建图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 8))
        fig.suptitle('太阳圈与虚点圈射线交点分析', fontsize=14)

        # 1. 几何关系图
        ax1.set_title('太阳圈与虚点圈几何关系图')

        # 绘制地球轨道
        earth_x = self.intersections_df['地球X坐标_AU'].values
        earth_y = self.intersections_df['地球Y坐标_AU'].values
        ax1.plot(earth_x, earth_y, 'b-', linewidth=1, alpha=0.7, label='地球轨道')

        # 绘制太阳圈（假设半径）
        sun_radius = 0.5  # 大概值
        theta = np.linspace(0, 2*np.pi, 100)
        sun_circle_x = sun_radius * np.cos(theta)
        sun_circle_y = sun_radius * np.sin(theta)
        ax1.plot(sun_circle_x, sun_circle_y, 'r-', linewidth=2, label='太阳圈')

        # 绘制虚点圈（假设参数）
        virtual_center_y = -0.033  # 大概值
        virtual_radius = 1.5  # 大概值
        virtual_circle_x = virtual_radius * np.cos(theta)
        virtual_circle_y = virtual_center_y + virtual_radius * np.sin(theta)
        ax1.plot(virtual_circle_x, virtual_circle_y, 'g-', linewidth=2, label='虚点圈')

        # 标记重要点
        ax1.plot(0, 0, 'yo', markersize=8, label='太阳')
        ax1.plot(0, virtual_center_y, 'go', markersize=6, label='虚点')

        ax1.set_xlabel('X坐标 (AU)')
        ax1.set_ylabel('Y坐标 (AU)')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        ax1.set_aspect('equal')

        # 2. 交点数量统计
        ax2.set_title('射线交点数量统计')

        # 统计各种交点数量
        sun_counts = self.intersections_df['太阳圈交点数量'].value_counts().sort_index()
        virtual_counts = self.intersections_df['虚点圈交点数量'].value_counts().sort_index()
        extra_counts = self.intersections_df['虚点额外点数量'].value_counts().sort_index()

        x_pos = np.arange(len(sun_counts))
        width = 0.25

        ax2.bar(x_pos - width, sun_counts.values, width, label='太阳圈交点', color='red', alpha=0.7)
        ax2.bar(x_pos, virtual_counts.values, width, label='虚点圈交点', color='green', alpha=0.7)
        ax2.bar(x_pos + width, extra_counts.values, width, label='虚点额外点', color='blue', alpha=0.7)

        ax2.set_xlabel('交点数量')
        ax2.set_ylabel('射线数量')
        ax2.set_xticks(x_pos)
        ax2.set_xticklabels(sun_counts.index)
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. 弧距分布
        ax3.set_title('虚点圈弧距分布')

        arc_distances = self.intersections_df['虚点圈弧距_AU'].dropna()
        if len(arc_distances) > 0:
            ax3.hist(arc_distances, bins=20, alpha=0.7, color='purple')
            ax3.set_xlabel('弧距 (AU)')
            ax3.set_ylabel('频次')
            ax3.grid(True, alpha=0.3)

        # 4. 圆心角变化
        ax4.set_title('圆心角随时间变化')

        days = self.intersections_df['天数']
        earth_angles = self.intersections_df['地球轨道圆心角_度']
        ax4.plot(days, earth_angles, 'b-', linewidth=1, label='地球轨道角')

        if '虚点圈交点圆心角_度' in self.intersections_df.columns:
            virtual_angles = self.intersections_df['虚点圈交点圆心角_度']
            ax4.plot(days, virtual_angles, 'g-', linewidth=1, label='虚点圈交点角')

        ax4.set_xlabel('天数')
        ax4.set_ylabel('圆心角 (度)')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()

        # 将图表嵌入到Tkinter中
        canvas = FigureCanvasTkAgg(fig, self.chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def create_data_table_from_results(self):
        """基于计算结果创建数据表格"""

        # 清空之前的表格
        for widget in self.table_frame.winfo_children():
            widget.destroy()

        # 创建表格框架
        table_container = ttk.Frame(self.table_frame)
        table_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建滚动条
        v_scrollbar = ttk.Scrollbar(table_container, orient=tk.VERTICAL)
        h_scrollbar = ttk.Scrollbar(table_container, orient=tk.HORIZONTAL)

        # 创建Treeview
        columns = list(self.intersections_df.columns)
        tree = ttk.Treeview(table_container, columns=columns, show='headings',
                           yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # 配置滚动条
        v_scrollbar.config(command=tree.yview)
        h_scrollbar.config(command=tree.xview)

        # 设置列标题和宽度
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=100, minwidth=80)

        # 插入数据（只显示前100行以提高性能）
        for i, row in self.intersections_df.head(100).iterrows():
            values = [f"{val:.6f}" if isinstance(val, (int, float)) and not pd.isna(val) else str(val)
                     for val in row]
            tree.insert('', tk.END, values=values)

        # 布局
        tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))

        table_container.columnconfigure(0, weight=1)
        table_container.rowconfigure(0, weight=1)

        # 添加说明标签
        info_label = ttk.Label(self.table_frame,
                              text=f"显示前100行数据，总共{len(self.intersections_df)}行")
        info_label.pack(pady=5)

    def display_parameters(self):
        """显示计算参数"""

        self.result_text.insert(tk.END, f"=== {self.orbital_params['planet_name']}轨道参数 ===\n")
        self.result_text.insert(tk.END, f"半长轴 a = {self.orbital_params['a']:.8f} AU\n")
        self.result_text.insert(tk.END, f"偏心率 e = {self.orbital_params['e']:.8f}\n")
        self.result_text.insert(tk.END, f"半短轴 b = {self.orbital_params['b']:.8f} AU\n")
        self.result_text.insert(tk.END, f"近日点距离 = {self.orbital_params['r_min']:.8f} AU\n")
        self.result_text.insert(tk.END, f"远日点距离 = {self.orbital_params['r_max']:.8f} AU\n\n")

        self.result_text.insert(tk.END, "=== 虚点参数 ===\n")
        self.result_text.insert(tk.END, f"虚点值 2ae = {self.virtual_params['virtual_point_value']:.8f} AU\n")
        self.result_text.insert(tk.END, f"虚点圈半径 2a - a(1-e²)/2 = {self.virtual_params['virtual_radius']:.8f} AU\n")
        vp = self.virtual_params['virtual_point']
        self.result_text.insert(tk.END, f"虚点坐标 = ({vp[0]:.8f}, {vp[1]:.8f}, {vp[2]:.8f}) AU\n\n")

    def display_results(self):
        """显示计算结果"""

        self.result_text.insert(tk.END, "=== 计算结果 ===\n")
        self.result_text.insert(tk.END, f"总射线数: {len(self.intersections_df)}\n")

        intersection_counts = self.intersections_df['交点数量'].value_counts().sort_index()
        for count, freq in intersection_counts.items():
            self.result_text.insert(tk.END, f"{count}个交点的射线: {freq}条\n")

        total_intersections = sum(self.intersections_df['交点数量'])
        self.result_text.insert(tk.END, f"总交点数: {total_intersections}\n")
        self.result_text.insert(tk.END, f"平均每条射线交点数: {total_intersections / len(self.intersections_df):.2f}\n\n")

        self.result_text.see(tk.END)

    def create_visualization(self):
        """创建可视化图表"""

        # 清空之前的图表
        for widget in self.chart_frame.winfo_children():
            widget.destroy()

        # 创建图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 8))
        fig.suptitle(f'{self.orbital_params["planet_name"]}虚点圆射线交点分析', fontsize=14)

        # 1. 轨道和虚点圆（XY平面视图）
        if 'x_position_au' in self.df.columns:
            ax1.plot(self.df['x_position_au'], self.df['y_position_au'], 'b-',
                    label=f'{self.orbital_params["planet_name"]}轨道', linewidth=2)

        ax1.scatter(0, 0, color='yellow', s=200, label='太阳', zorder=5)

        # 绘制虚点
        virtual_point = self.virtual_params['virtual_point']
        ax1.scatter(virtual_point[0], virtual_point[1], color='red', s=100, label='虚点', zorder=5)

        # 绘制虚点圆
        theta = np.linspace(0, 2*np.pi, 100)
        virtual_radius = self.virtual_params['virtual_radius']
        circle_x = virtual_point[0] + virtual_radius * np.cos(theta)
        circle_y = virtual_point[1] + virtual_radius * np.sin(theta)

        ax1.plot(circle_x, circle_y, 'r--', label='虚点圆', linewidth=2)
        ax1.set_xlabel('X (AU)')
        ax1.set_ylabel('Y (AU)')
        ax1.set_title('轨道与虚点圆 (XY平面视图)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_aspect('equal')

        # 2. 交点分布
        all_intersections_x = []
        all_intersections_y = []

        for _, row in self.intersections_df.iterrows():
            for j in range(row['交点数量']):
                col_x = f'交点{j+1}_X坐标_AU'
                col_y = f'交点{j+1}_Y坐标_AU'
                if col_x in row and col_y in row:
                    all_intersections_x.append(row[col_x])
                    all_intersections_y.append(row[col_y])

        if all_intersections_x:
            ax2.scatter(all_intersections_x, all_intersections_y,
                       c='green', s=20, alpha=0.6, label='交点')
            ax2.plot(circle_x, circle_y, 'r--', label='虚点圆', linewidth=2)
            ax2.scatter(virtual_point[0], virtual_point[1], color='red', s=100, label='虚点')

        ax2.set_xlabel('X (AU)')
        ax2.set_ylabel('Y (AU)')
        ax2.set_title('射线与虚点圆交点分布')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.set_aspect('equal')

        # 3. 交点数量统计
        intersection_counts = self.intersections_df['交点数量'].value_counts().sort_index()
        ax3.bar(intersection_counts.index, intersection_counts.values, color='skyblue')
        ax3.set_xlabel('交点数量')
        ax3.set_ylabel('射线数量')
        ax3.set_title('交点数量分布')
        ax3.grid(True, alpha=0.3)

        # 4. 交点数量随时间变化
        days = self.intersections_df['天数']
        counts = self.intersections_df['交点数量']
        ax4.plot(days, counts, 'g-', linewidth=1)
        ax4.set_xlabel('天数')
        ax4.set_ylabel('交点数量')
        ax4.set_title('交点数量随时间变化')
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()

        # 将图表嵌入到Tkinter中
        canvas = FigureCanvasTkAgg(fig, self.chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def create_data_table(self):
        """创建数据表格"""

        # 清空之前的表格
        for widget in self.table_frame.winfo_children():
            widget.destroy()

        # 创建表格框架
        table_frame = ttk.Frame(self.table_frame)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 添加说明标签
        header_text = """交点数据表说明：
• 天数：从第1天开始的序号
• 日期：对应的日期
• 儒略日：儒略日数
• 地球坐标：地球在太阳系中的位置 (AU)
• 射线方向：从太阳指向地球XY坐标的方向向量
• 交点数量：该射线与虚点圆的交点个数
• 交点坐标：具体交点的三维坐标 (AU)"""

        header_label = ttk.Label(table_frame, text=header_text, justify=tk.LEFT,
                                font=('Arial', 9), foreground='blue')
        header_label.pack(anchor=tk.W, pady=(0, 10))

        # 创建Treeview表格
        columns = list(self.intersections_df.columns)
        tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # 设置列标题
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=100, anchor=tk.CENTER)

        # 添加数据
        for _, row in self.intersections_df.iterrows():
            values = [f"{row[col]:.6f}" if isinstance(row[col], (int, float)) and col != '天数'
                     else str(row[col]) for col in columns]
            tree.insert('', tk.END, values=values)

        # 添加滚动条
        scrollbar_y = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=tree.xview)
        tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # 布局
        tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        scrollbar_x.grid(row=1, column=0, sticky=(tk.W, tk.E))

        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

    def save_results(self):
        """保存计算结果"""

        try:
            # 创建输出目录
            output_dir = 'planet_data'
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # 保存交点数据（带中文表头说明）
            intersections_file = os.path.join(output_dir, f'{self.orbital_params["planet_name"]}_虚点圆交点数据.csv')

            # 创建带说明的CSV文件
            with open(intersections_file, 'w', encoding='utf-8-sig', newline='') as f:
                # 写入中文说明
                f.write("# 虚点圆射线交点计算数据表\n")
                f.write("# 数据说明：\n")
                f.write("# 天数：从第1天开始的序号\n")
                f.write("# 日期：对应的日期（TDB时间）\n")
                f.write("# 儒略日：儒略日数\n")
                f.write("# 地球坐标：地球在太阳系中的位置坐标，单位：天文单位(AU)\n")
                f.write("# 射线方向：从太阳指向地球XY坐标的方向向量\n")
                f.write("# 交点数量：该射线与虚点圆的交点个数\n")
                f.write("# 交点坐标：具体交点的三维坐标，单位：天文单位(AU)\n")
                f.write("# 虚点圆：圆心在虚点，半径为虚半径，位于XY平面（Z=0）\n")
                f.write("# 计算方法：射线从太阳出发，指向地球的XY投影，与虚点圆求交\n")
                f.write("#\n")

                # 写入数据
                self.intersections_df.to_csv(f, index=False)

            # 保存计算参数
            params_file = os.path.join(output_dir, f'{self.orbital_params["planet_name"]}_虚点圆计算参数.txt')
            with open(params_file, 'w', encoding='utf-8') as f:
                f.write(f"{self.orbital_params['planet_name']}虚点圆射线交点计算参数\n")
                f.write("=" * 50 + "\n\n")

                f.write("轨道参数 (JPL官方标准值):\n")
                f.write(f"半长轴 a = {self.orbital_params['a']:.8f} AU\n")
                f.write(f"偏心率 e = {self.orbital_params['e']:.8f}\n")
                f.write(f"半短轴 b = {self.orbital_params['b']:.8f} AU\n")
                f.write(f"近日点距离 = {self.orbital_params['r_min']:.8f} AU\n")
                f.write(f"远日点距离 = {self.orbital_params['r_max']:.8f} AU\n\n")

                f.write("虚点参数:\n")
                f.write(f"虚点值 2ae = {self.virtual_params['virtual_point_value']:.8f} AU\n")
                f.write(f"虚点圈半径 2a - a(1-e²)/2 = {self.virtual_params['virtual_radius']:.8f} AU\n")
                vp = self.virtual_params['virtual_point']
                f.write(f"虚点坐标 = ({vp[0]:.8f}, {vp[1]:.8f}, {vp[2]:.8f}) AU\n\n")

                f.write("交点统计:\n")
                intersection_counts = self.intersections_df['交点数量'].value_counts().sort_index()
                for count, freq in intersection_counts.items():
                    f.write(f"{count}个交点的射线: {freq}条\n")

                total_intersections = sum(self.intersections_df['交点数量'])
                f.write(f"\n总交点数: {total_intersections}\n")
                f.write(f"平均每条射线交点数: {total_intersections / len(self.intersections_df):.2f}\n")

            self.result_text.insert(tk.END, f"[成功] 结果已保存到:\n")
            self.result_text.insert(tk.END, f"  数据文件: {intersections_file}\n")
            self.result_text.insert(tk.END, f"  参数文件: {params_file}\n")
            self.result_text.see(tk.END)

        except Exception as e:
            messagebox.showerror("错误", f"保存文件失败: {e}")

def main():
    """主函数"""
    root = tk.Tk()
    app = SunVirtualCircleCalculator(root)
    root.mainloop()

if __name__ == "__main__":
    main()
