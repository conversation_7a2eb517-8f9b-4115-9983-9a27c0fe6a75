#!/usr/bin/env python3
"""
测试编码修复的脚本
模拟GUI调用外部计算脚本的过程
"""

import subprocess
import sys
import os

def test_subprocess_call():
    """测试subprocess调用是否能正确处理编码"""
    
    print("测试subprocess调用外部计算脚本...")
    print("=" * 50)
    
    try:
        # 使用修复后的subprocess调用方式
        result = subprocess.run([
            sys.executable, "太阳圈虚点圈交点计算.py"
        ], capture_output=True, text=True, cwd=os.getcwd(), 
        encoding='utf-8', errors='replace')
        
        print(f"返回码: {result.returncode}")
        print(f"标准输出长度: {len(result.stdout)} 字符")
        print(f"标准错误长度: {len(result.stderr)} 字符")
        
        if result.returncode == 0:
            print("[成功] 计算脚本执行成功！")
            print("\n--- 输出前500字符 ---")
            print(result.stdout[:500])
            if len(result.stdout) > 500:
                print("...")
        else:
            print("[错误] 计算脚本执行失败:")
            print("标准错误输出:")
            print(result.stderr)
            
    except Exception as e:
        print(f"[异常] 调用过程中发生异常: {e}")

if __name__ == "__main__":
    test_subprocess_call()
