#!/usr/bin/env python3
"""
太阳圈与虚点圈射线交点计算程序
使用JPL官方标准轨道参数

新增功能：
1. 太阳圈：以太阳为圆心，半径 a(1-e^2)/2
2. 计算从太阳指向地球的射线与太阳圈的交点
3. 计算从虚点指向地球的射线与虚点圈的交点
4. 生成中文表头的CSV文件
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_earth_orbit_data(file_path='planet_data/地球_2024_1天_处理后数据.csv'):
    """加载地球轨道数据"""

    if not os.path.exists(file_path):
        print(f"[错误] 数据文件不存在: {file_path}")
        print("请先运行数据处理脚本生成轨道数据")
        return None

    try:
        df = pd.read_csv(file_path)
        print(f"[成功] 成功加载轨道数据，共 {len(df)} 个数据点")
        return df
    except Exception as e:
        print(f"[错误] 加载数据失败: {e}")
        return None

def get_jpl_standard_orbital_parameters():
    """获取JPL官方标准轨道参数"""
    
    # JPL官方标准值 (1800-2050 AD, Table 1)
    a = 1.00000261  # 半长轴 (AU)
    e = 0.01671123  # 偏心率
    
    # 计算其他参数
    b = a * np.sqrt(1 - e**2)  # 半短轴
    r_min = a * (1 - e)        # 近日点
    r_max = a * (1 + e)        # 远日点
    
    return {
        'a': a,
        'e': e,
        'b': b,
        'r_min': r_min,
        'r_max': r_max
    }

def calculate_circle_parameters(orbital_params):
    """计算太阳圈和虚点圈参数"""

    a = orbital_params['a']
    e = orbital_params['e']

    # 太阳圈参数
    sun_circle_radius = a * (1 - e**2) / 2  # 半径 = a(1-e^2)/2
    sun_circle_center = np.array([0, 0, 0])  # 圆心在太阳

    # 虚点圈参数
    virtual_point_value = 2 * a * e  # 虚点值 = 2ae
    virtual_circle_radius = 2 * a - sun_circle_radius  # 虚半径 = 2a - a(1-e^2)/2
    virtual_circle_center = np.array([0, -virtual_point_value, 0])  # 虚点坐标

    return {
        'sun_circle': {
            'center': sun_circle_center,
            'radius': sun_circle_radius
        },
        'virtual_circle': {
            'center': virtual_circle_center,
            'radius': virtual_circle_radius
        }
    }

def calculate_ray_circle_intersection(ray_origin, ray_direction, circle_center, circle_radius):
    """计算射线与圆的交点（在XY平面内）"""
    
    # 确保在XY平面内计算（Z=0）
    ray_origin_2d = np.array([ray_origin[0], ray_origin[1]])
    ray_direction_2d = np.array([ray_direction[0], ray_direction[1]])
    circle_center_2d = np.array([circle_center[0], circle_center[1]])
    
    # 标准化射线方向
    if np.linalg.norm(ray_direction_2d) < 1e-10:
        return []
    
    ray_direction_2d = ray_direction_2d / np.linalg.norm(ray_direction_2d)
    
    # 设 oc = ray_origin_2d - circle_center_2d
    oc = ray_origin_2d - circle_center_2d
    
    # 展开得到二次方程: a*t^2 + b*t + c = 0
    a = np.dot(ray_direction_2d, ray_direction_2d)  # 应该等于1（已标准化）
    b = 2.0 * np.dot(oc, ray_direction_2d)
    c = np.dot(oc, oc) - circle_radius**2
    
    # 计算判别式
    discriminant = b**2 - 4*a*c
    
    if discriminant < 0:
        return []
    elif discriminant == 0:
        t = -b / (2*a)
        if t >= 0:
            intersection_2d = ray_origin_2d + t * ray_direction_2d
            intersection_3d = np.array([intersection_2d[0], intersection_2d[1], 0])
            return [intersection_3d]
        else:
            return []
    else:
        sqrt_discriminant = np.sqrt(discriminant)
        t1 = (-b - sqrt_discriminant) / (2*a)
        t2 = (-b + sqrt_discriminant) / (2*a)
        
        intersections = []
        
        if t1 >= 0:
            intersection_2d = ray_origin_2d + t1 * ray_direction_2d
            intersection_3d = np.array([intersection_2d[0], intersection_2d[1], 0])
            intersections.append(intersection_3d)
        
        if t2 >= 0:
            intersection_2d = ray_origin_2d + t2 * ray_direction_2d
            intersection_3d = np.array([intersection_2d[0], intersection_2d[1], 0])
            intersections.append(intersection_3d)
        
        return intersections

def calculate_central_angle(point_x, point_y, center_x=0, center_y=0):
    """计算点相对于圆心的圆心角（从x轴正方向开始，逆时针为正）"""
    angle = np.arctan2(point_y - center_y, point_x - center_x)
    # 将角度转换为0到2π范围
    if angle < 0:
        angle += 2 * np.pi
    return angle

def calculate_arc_distance(angle1, angle2, radius):
    """计算两个角度之间的弧长距离"""
    # 计算角度差（取较小的弧）
    angle_diff = abs(angle2 - angle1)
    if angle_diff > np.pi:
        angle_diff = 2 * np.pi - angle_diff
    return angle_diff * radius

def calculate_all_intersections(df, orbital_params, circle_params):
    """计算所有射线与圆的交点，包括虚点额外点和圆心角"""

    sun_circle = circle_params['sun_circle']
    virtual_circle = circle_params['virtual_circle']

    intersections_data = []

    print(f"\n开始计算 {len(df)} 条射线的交点...")
    print(f"太阳圈: 圆心 = ({sun_circle['center'][0]:.6f}, {sun_circle['center'][1]:.6f}, {sun_circle['center'][2]:.6f}), 半径 = {sun_circle['radius']:.6f} AU")
    print(f"虚点圈: 圆心 = ({virtual_circle['center'][0]:.6f}, {virtual_circle['center'][1]:.6f}, {virtual_circle['center'][2]:.6f}), 半径 = {virtual_circle['radius']:.6f} AU")
    
    for i, row in df.iterrows():
        # 地球位置
        earth_position = np.array([
            row['x_position_au'],
            row['y_position_au'],
            row['z_position_au']
        ])

        # 1. 从太阳指向地球的射线与太阳圈的交点
        sun_ray_direction = np.array([earth_position[0], earth_position[1], 0])  # 只取XY分量
        sun_intersections = calculate_ray_circle_intersection(
            sun_circle['center'], sun_ray_direction, sun_circle['center'], sun_circle['radius']
        )

        # 2. 从虚点指向地球的射线与虚点圈的交点
        virtual_ray_direction = np.array([
            earth_position[0] - virtual_circle['center'][0],
            earth_position[1] - virtual_circle['center'][1],
            0
        ])
        virtual_intersections = calculate_ray_circle_intersection(
            virtual_circle['center'], virtual_ray_direction, virtual_circle['center'], virtual_circle['radius']
        )

        # 3. 从太阳指向地球的射线与虚点圈的交点（虚点额外点）
        virtual_extra_intersections = calculate_ray_circle_intersection(
            sun_circle['center'], sun_ray_direction, virtual_circle['center'], virtual_circle['radius']
        )
        
        # 计算圆心角（从x轴正方向开始，逆时针为正）
        # 地球轨道圆心角（以太阳为圆心）
        earth_angle = calculate_central_angle(earth_position[0], earth_position[1], 0, 0)

        # 太阳圈交点圆心角
        sun_intersection_angle = None
        if sun_intersections:
            sun_intersection_angle = calculate_central_angle(
                sun_intersections[0][0], sun_intersections[0][1], 0, 0
            )

        # 虚点圈交点圆心角（以虚点为圆心）
        virtual_intersection_angle = None
        if virtual_intersections:
            virtual_intersection_angle = calculate_central_angle(
                virtual_intersections[0][0], virtual_intersections[0][1],
                virtual_circle['center'][0], virtual_circle['center'][1]
            )

        # 虚点额外点圆心角（以虚点为圆心）
        virtual_extra_angle = None
        if virtual_extra_intersections:
            virtual_extra_angle = calculate_central_angle(
                virtual_extra_intersections[0][0], virtual_extra_intersections[0][1],
                virtual_circle['center'][0], virtual_circle['center'][1]
            )

        # 计算弧距（虚点圈上的弧长）
        virtual_arc_distance = None
        if virtual_intersection_angle is not None and virtual_extra_angle is not None:
            virtual_arc_distance = calculate_arc_distance(
                virtual_intersection_angle, virtual_extra_angle, virtual_circle['radius']
            )

        # 记录结果
        date = row.get('date', f'Day {i+1}')
        julian_date = row.get('julian_date', 0)

        intersection_record = {
            '天数': i + 1,
            '日期': date,
            '儒略日': julian_date,
            '地球X坐标_AU': earth_position[0],
            '地球Y坐标_AU': earth_position[1],
            '地球Z坐标_AU': earth_position[2],

            # 太阳圈相关
            '太阳射线方向X': sun_ray_direction[0],
            '太阳射线方向Y': sun_ray_direction[1],
            '太阳射线方向Z': sun_ray_direction[2],
            '太阳圈交点数量': len(sun_intersections),

            # 虚点圈相关
            '虚点射线方向X': virtual_ray_direction[0],
            '虚点射线方向Y': virtual_ray_direction[1],
            '虚点射线方向Z': virtual_ray_direction[2],
            '虚点圈交点数量': len(virtual_intersections),

            # 虚点额外点相关
            '虚点额外点数量': len(virtual_extra_intersections),

            # 圆心角（弧度）
            '地球轨道圆心角_弧度': earth_angle,
            '太阳圈交点圆心角_弧度': sun_intersection_angle,
            '虚点圈交点圆心角_弧度': virtual_intersection_angle,
            '虚点额外点圆心角_弧度': virtual_extra_angle,

            # 圆心角（度）
            '地球轨道圆心角_度': np.degrees(earth_angle),
            '太阳圈交点圆心角_度': np.degrees(sun_intersection_angle) if sun_intersection_angle is not None else None,
            '虚点圈交点圆心角_度': np.degrees(virtual_intersection_angle) if virtual_intersection_angle is not None else None,
            '虚点额外点圆心角_度': np.degrees(virtual_extra_angle) if virtual_extra_angle is not None else None,

            # 弧距
            '虚点圈弧距_AU': virtual_arc_distance,
            '虚点圈弧距_km': virtual_arc_distance * 149597870.7 if virtual_arc_distance is not None else None
        }
        
        # 添加太阳圈交点坐标
        for j, intersection in enumerate(sun_intersections):
            intersection_record[f'太阳圈交点{j+1}_X坐标_AU'] = intersection[0]
            intersection_record[f'太阳圈交点{j+1}_Y坐标_AU'] = intersection[1]
            intersection_record[f'太阳圈交点{j+1}_Z坐标_AU'] = intersection[2]

        # 添加虚点圈交点坐标
        for j, intersection in enumerate(virtual_intersections):
            intersection_record[f'虚点圈交点{j+1}_X坐标_AU'] = intersection[0]
            intersection_record[f'虚点圈交点{j+1}_Y坐标_AU'] = intersection[1]
            intersection_record[f'虚点圈交点{j+1}_Z坐标_AU'] = intersection[2]

        # 添加虚点额外点坐标
        for j, intersection in enumerate(virtual_extra_intersections):
            intersection_record[f'虚点额外点{j+1}_X坐标_AU'] = intersection[0]
            intersection_record[f'虚点额外点{j+1}_Y坐标_AU'] = intersection[1]
            intersection_record[f'虚点额外点{j+1}_Z坐标_AU'] = intersection[2]
        
        intersections_data.append(intersection_record)
        
        if (i + 1) % 50 == 0:
            print(f"已处理 {i + 1}/{len(df)} 条射线")
    
    return pd.DataFrame(intersections_data)

def save_results(orbital_params, circle_params, intersections_df, output_dir='planet_data'):
    """保存计算结果"""
    
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 保存交点数据（带中文表头说明）
    import time
    timestamp = int(time.time())
    intersections_file = os.path.join(output_dir, f'太阳圈虚点圈交点数据_{timestamp}.csv')

    # 如果文件存在，先删除
    if os.path.exists(intersections_file):
        try:
            os.remove(intersections_file)
        except:
            pass

    # 创建带说明的CSV文件
    with open(intersections_file, 'w', encoding='utf-8-sig', newline='') as f:
        # 写入中文说明
        f.write("# 太阳圈与虚点圈射线交点计算数据表\n")
        f.write("# 数据说明：\n")
        f.write("# 天数：从第1天开始的序号\n")
        f.write("# 日期：对应的日期（TDB时间）\n")
        f.write("# 儒略日：儒略日数\n")
        f.write("# 地球坐标：地球在太阳系中的位置坐标，单位：天文单位(AU)\n")
        f.write("# 太阳射线：从太阳指向地球XY坐标的射线\n")
        f.write("# 虚点射线：从虚点指向地球XY坐标的射线\n")
        f.write("# 太阳圈：以太阳为圆心，半径 a(1-e^2)/2，位于XY平面（Z=0）\n")
        f.write("# 虚点圈：以虚点为圆心，半径 2a - a(1-e^2)/2，位于XY平面（Z=0）\n")
        f.write("# 虚点额外点：太阳射线与虚点圈的交点\n")
        f.write("# 圆心角：各点相对于对应圆心的角度（从x轴正方向开始，逆时针为正）\n")
        f.write("# 弧距：虚点圈交点与虚点额外点之间的弧长距离\n")
        f.write("# 交点坐标：具体交点的三维坐标，单位：天文单位(AU)\n")
        f.write("#\n")
        
        # 写入数据
        intersections_df.to_csv(f, index=False)
    
    print(f"[成功] 交点数据已保存到: {intersections_file}")

    # 保存计算参数
    params_file = os.path.join(output_dir, '太阳圈虚点圈计算参数.txt')
    with open(params_file, 'w', encoding='utf-8') as f:
        f.write("太阳圈与虚点圈射线交点计算参数\n")
        f.write("=" * 50 + "\n\n")
        
        f.write("轨道参数 (JPL官方标准值):\n")
        f.write(f"半长轴 a = {orbital_params['a']:.8f} AU\n")
        f.write(f"偏心率 e = {orbital_params['e']:.8f}\n")
        f.write(f"半短轴 b = {orbital_params['b']:.8f} AU\n")
        f.write(f"近日点距离 = {orbital_params['r_min']:.8f} AU\n")
        f.write(f"远日点距离 = {orbital_params['r_max']:.8f} AU\n\n")
        
        f.write("太阳圈参数:\n")
        sun_circle = circle_params['sun_circle']
        f.write(f"圆心坐标 = ({sun_circle['center'][0]:.8f}, {sun_circle['center'][1]:.8f}, {sun_circle['center'][2]:.8f}) AU\n")
        f.write(f"半径 a(1-e^2)/2 = {sun_circle['radius']:.8f} AU\n")
        f.write(f"半径 (km) = {sun_circle['radius'] * 149597870.7:.1f} km\n\n")
        
        f.write("虚点圈参数:\n")
        virtual_circle = circle_params['virtual_circle']
        f.write(f"圆心坐标 = ({virtual_circle['center'][0]:.8f}, {virtual_circle['center'][1]:.8f}, {virtual_circle['center'][2]:.8f}) AU\n")
        f.write(f"半径 2a - a(1-e^2)/2 = {virtual_circle['radius']:.8f} AU\n")
        f.write(f"半径 (km) = {virtual_circle['radius'] * 149597870.7:.1f} km\n\n")
        
        f.write("交点统计:\n")
        
        # 太阳圈交点统计
        sun_intersection_counts = intersections_df['太阳圈交点数量'].value_counts().sort_index()
        f.write("太阳圈交点统计:\n")
        for count, freq in sun_intersection_counts.items():
            f.write(f"  {count}个交点的射线: {freq}条\n")
        
        total_sun_intersections = sum(intersections_df['太阳圈交点数量'])
        f.write(f"  总交点数: {total_sun_intersections}\n")
        f.write(f"  平均每条射线交点数: {total_sun_intersections / len(intersections_df):.2f}\n\n")
        
        # 虚点圈交点统计
        virtual_intersection_counts = intersections_df['虚点圈交点数量'].value_counts().sort_index()
        f.write("虚点圈交点统计:\n")
        for count, freq in virtual_intersection_counts.items():
            f.write(f"  {count}个交点的射线: {freq}条\n")
        
        total_virtual_intersections = sum(intersections_df['虚点圈交点数量'])
        f.write(f"  总交点数: {total_virtual_intersections}\n")
        f.write(f"  平均每条射线交点数: {total_virtual_intersections / len(intersections_df):.2f}\n\n")

        # 虚点额外点统计
        virtual_extra_intersection_counts = intersections_df['虚点额外点数量'].value_counts().sort_index()
        f.write("虚点额外点统计:\n")
        for count, freq in virtual_extra_intersection_counts.items():
            f.write(f"  {count}个交点的射线: {freq}条\n")

        total_virtual_extra_intersections = sum(intersections_df['虚点额外点数量'])
        f.write(f"  总交点数: {total_virtual_extra_intersections}\n")
        f.write(f"  平均每条射线交点数: {total_virtual_extra_intersections / len(intersections_df):.2f}\n\n")

        # 弧距统计
        valid_arc_distances = intersections_df['虚点圈弧距_AU'].dropna()
        if len(valid_arc_distances) > 0:
            f.write("虚点圈弧距统计:\n")
            f.write(f"  平均弧距: {valid_arc_distances.mean():.6f} AU ({valid_arc_distances.mean() * 149597870.7:.1f} km)\n")
            f.write(f"  最大弧距: {valid_arc_distances.max():.6f} AU ({valid_arc_distances.max() * 149597870.7:.1f} km)\n")
            f.write(f"  最小弧距: {valid_arc_distances.min():.6f} AU ({valid_arc_distances.min() * 149597870.7:.1f} km)\n")
    
    print(f"[成功] 计算参数已保存到: {params_file}")

def create_visualization(orbital_params, circle_params, intersections_df, output_dir='planet_data'):
    """创建可视化图表"""

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('太阳圈与虚点圈射线交点分析图表', fontsize=16, fontweight='bold')

    # 1. 轨道与圆的几何关系图
    ax1.set_title('太阳圈与虚点圈几何关系图', fontsize=12, fontweight='bold')

    # 绘制地球轨道
    earth_x = intersections_df['地球X坐标_AU'].values
    earth_y = intersections_df['地球Y坐标_AU'].values
    ax1.plot(earth_x, earth_y, 'b-', linewidth=1, alpha=0.7, label='地球轨道')

    # 绘制太阳圈
    sun_circle = circle_params['sun_circle']
    theta = np.linspace(0, 2*np.pi, 100)
    sun_circle_x = sun_circle['center'][0] + sun_circle['radius'] * np.cos(theta)
    sun_circle_y = sun_circle['center'][1] + sun_circle['radius'] * np.sin(theta)
    ax1.plot(sun_circle_x, sun_circle_y, 'r-', linewidth=2, label=f'太阳圈 (r={sun_circle["radius"]:.4f} AU)')

    # 绘制虚点圈
    virtual_circle = circle_params['virtual_circle']
    virtual_circle_x = virtual_circle['center'][0] + virtual_circle['radius'] * np.cos(theta)
    virtual_circle_y = virtual_circle['center'][1] + virtual_circle['radius'] * np.sin(theta)
    ax1.plot(virtual_circle_x, virtual_circle_y, 'g-', linewidth=2, label=f'虚点圈 (r={virtual_circle["radius"]:.4f} AU)')

    # 标记重要点
    ax1.plot(0, 0, 'yo', markersize=8, label='太阳')
    ax1.plot(virtual_circle['center'][0], virtual_circle['center'][1], 'go', markersize=6, label='虚点')

    # 绘制一些示例射线和交点
    sample_indices = [0, 90, 180, 270]  # 选择几个代表性的点
    for i in sample_indices:
        if i < len(intersections_df):
            row = intersections_df.iloc[i]

            # 太阳射线
            if row['太阳圈交点数量'] > 0:
                sun_intersection_x = row.get('太阳圈交点1_X坐标_AU', 0)
                sun_intersection_y = row.get('太阳圈交点1_Y坐标_AU', 0)
                ax1.plot([0, sun_intersection_x], [0, sun_intersection_y], 'r--', alpha=0.5, linewidth=1)
                ax1.plot(sun_intersection_x, sun_intersection_y, 'ro', markersize=4)

            # 虚点射线
            if row['虚点圈交点数量'] > 0:
                virtual_intersection_x = row.get('虚点圈交点1_X坐标_AU', 0)
                virtual_intersection_y = row.get('虚点圈交点1_Y坐标_AU', 0)
                ax1.plot([virtual_circle['center'][0], virtual_intersection_x],
                        [virtual_circle['center'][1], virtual_intersection_y], 'g--', alpha=0.5, linewidth=1)
                ax1.plot(virtual_intersection_x, virtual_intersection_y, 'go', markersize=4)

    ax1.set_xlabel('X坐标 (AU)')
    ax1.set_ylabel('Y坐标 (AU)')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    ax1.set_aspect('equal')

    # 2. 交点数量统计
    ax2.set_title('射线交点数量统计', fontsize=12, fontweight='bold')

    sun_counts = intersections_df['太阳圈交点数量'].value_counts().sort_index()
    virtual_counts = intersections_df['虚点圈交点数量'].value_counts().sort_index()

    x_pos = np.arange(len(sun_counts))
    width = 0.35

    bars1 = ax2.bar(x_pos - width/2, sun_counts.values, width, label='太阳圈交点', color='red', alpha=0.7)
    bars2 = ax2.bar(x_pos + width/2, virtual_counts.values, width, label='虚点圈交点', color='green', alpha=0.7)

    ax2.set_xlabel('交点数量')
    ax2.set_ylabel('射线数量')
    ax2.set_xticks(x_pos)
    ax2.set_xticklabels(sun_counts.index)
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # 添加数值标签
    for bar in bars1:
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{int(height)}', ha='center', va='bottom')

    for bar in bars2:
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{int(height)}', ha='center', va='bottom')

    # 3. 交点坐标分布（太阳圈）
    ax3.set_title('太阳圈交点坐标分布', fontsize=12, fontweight='bold')

    # 收集所有太阳圈交点
    sun_intersection_x = []
    sun_intersection_y = []
    for _, row in intersections_df.iterrows():
        if row['太阳圈交点数量'] > 0:
            sun_intersection_x.append(row.get('太阳圈交点1_X坐标_AU', 0))
            sun_intersection_y.append(row.get('太阳圈交点1_Y坐标_AU', 0))

    if sun_intersection_x:
        ax3.scatter(sun_intersection_x, sun_intersection_y, c='red', alpha=0.6, s=20, label='太阳圈交点')

        # 绘制太阳圈
        ax3.plot(sun_circle_x, sun_circle_y, 'r-', linewidth=2, alpha=0.8, label='太阳圈')
        ax3.plot(0, 0, 'yo', markersize=8, label='太阳')

    ax3.set_xlabel('X坐标 (AU)')
    ax3.set_ylabel('Y坐标 (AU)')
    ax3.grid(True, alpha=0.3)
    ax3.legend()
    ax3.set_aspect('equal')

    # 4. 交点坐标分布（虚点圈）
    ax4.set_title('虚点圈交点坐标分布', fontsize=12, fontweight='bold')

    # 收集所有虚点圈交点
    virtual_intersection_x = []
    virtual_intersection_y = []
    for _, row in intersections_df.iterrows():
        if row['虚点圈交点数量'] > 0:
            virtual_intersection_x.append(row.get('虚点圈交点1_X坐标_AU', 0))
            virtual_intersection_y.append(row.get('虚点圈交点1_Y坐标_AU', 0))

    if virtual_intersection_x:
        ax4.scatter(virtual_intersection_x, virtual_intersection_y, c='green', alpha=0.6, s=20, label='虚点圈交点')

        # 绘制虚点圈
        ax4.plot(virtual_circle_x, virtual_circle_y, 'g-', linewidth=2, alpha=0.8, label='虚点圈')
        ax4.plot(virtual_circle['center'][0], virtual_circle['center'][1], 'go', markersize=8, label='虚点')

    ax4.set_xlabel('X坐标 (AU)')
    ax4.set_ylabel('Y坐标 (AU)')
    ax4.grid(True, alpha=0.3)
    ax4.legend()
    ax4.set_aspect('equal')

    plt.tight_layout()

    # 保存图表
    chart_file = os.path.join(output_dir, '太阳圈虚点圈交点分析图表.png')
    plt.savefig(chart_file, dpi=300, bbox_inches='tight')
    print(f"[成功] 分析图表已保存到: {chart_file}")

    plt.show()

def main():
    """主函数"""
    print("太阳圈与虚点圈射线交点计算程序 (使用JPL官方标准值)")
    print("=" * 60)

    # 1. 加载地球轨道数据
    df = load_earth_orbit_data()
    if df is None:
        return

    # 2. 使用JPL官方标准轨道参数
    print("\n使用JPL官方标准轨道参数:")
    orbital_params = get_jpl_standard_orbital_parameters()

    print(f"半长轴 a = {orbital_params['a']:.8f} AU (JPL标准值)")
    print(f"偏心率 e = {orbital_params['e']:.8f} (JPL标准值)")
    print(f"半短轴 b = {orbital_params['b']:.8f} AU")
    print(f"近日点距离 = {orbital_params['r_min']:.8f} AU")
    print(f"远日点距离 = {orbital_params['r_max']:.8f} AU")

    # 3. 计算圆参数
    circle_params = calculate_circle_parameters(orbital_params)

    print(f"\n太阳圈参数:")
    sun_circle = circle_params['sun_circle']
    print(f"圆心坐标 = ({sun_circle['center'][0]:.8f}, {sun_circle['center'][1]:.8f}, {sun_circle['center'][2]:.8f}) AU")
    print(f"半径 a(1-e^2)/2 = {sun_circle['radius']:.8f} AU ({sun_circle['radius'] * 149597870.7:.1f} km)")

    print(f"\n虚点圈参数:")
    virtual_circle = circle_params['virtual_circle']
    print(f"圆心坐标 = ({virtual_circle['center'][0]:.8f}, {virtual_circle['center'][1]:.8f}, {virtual_circle['center'][2]:.8f}) AU")
    print(f"半径 2a - a(1-e^2)/2 = {virtual_circle['radius']:.8f} AU ({virtual_circle['radius'] * 149597870.7:.1f} km)")

    # 4. 计算交点
    intersections_df = calculate_all_intersections(df, orbital_params, circle_params)

    # 5. 显示统计结果
    print(f"\n计算完成！统计结果:")
    print(f"总射线数: {len(intersections_df)}")

    # 太阳圈统计
    print(f"\n太阳圈交点统计:")
    sun_intersection_counts = intersections_df['太阳圈交点数量'].value_counts().sort_index()
    for count, freq in sun_intersection_counts.items():
        print(f"{count}个交点的射线: {freq}条")

    total_sun_intersections = sum(intersections_df['太阳圈交点数量'])
    print(f"总交点数: {total_sun_intersections}")
    print(f"平均每条射线交点数: {total_sun_intersections / len(intersections_df):.2f}")

    # 虚点圈统计
    print(f"\n虚点圈交点统计:")
    virtual_intersection_counts = intersections_df['虚点圈交点数量'].value_counts().sort_index()
    for count, freq in virtual_intersection_counts.items():
        print(f"{count}个交点的射线: {freq}条")

    total_virtual_intersections = sum(intersections_df['虚点圈交点数量'])
    print(f"总交点数: {total_virtual_intersections}")
    print(f"平均每条射线交点数: {total_virtual_intersections / len(intersections_df):.2f}")

    # 虚点额外点统计
    print(f"\n虚点额外点统计:")
    virtual_extra_intersection_counts = intersections_df['虚点额外点数量'].value_counts().sort_index()
    for count, freq in virtual_extra_intersection_counts.items():
        print(f"{count}个交点的射线: {freq}条")

    total_virtual_extra_intersections = sum(intersections_df['虚点额外点数量'])
    print(f"总交点数: {total_virtual_extra_intersections}")
    print(f"平均每条射线交点数: {total_virtual_extra_intersections / len(intersections_df):.2f}")

    # 弧距统计
    valid_arc_distances = intersections_df['虚点圈弧距_AU'].dropna()
    if len(valid_arc_distances) > 0:
        print(f"\n虚点圈弧距统计:")
        print(f"平均弧距: {valid_arc_distances.mean():.6f} AU ({valid_arc_distances.mean() * 149597870.7:.1f} km)")
        print(f"最大弧距: {valid_arc_distances.max():.6f} AU ({valid_arc_distances.max() * 149597870.7:.1f} km)")
        print(f"最小弧距: {valid_arc_distances.min():.6f} AU ({valid_arc_distances.min() * 149597870.7:.1f} km)")

    # 6. 保存结果
    save_results(orbital_params, circle_params, intersections_df)

    # 7. 创建可视化图表
    create_visualization(orbital_params, circle_params, intersections_df)

    print(f"\n[完成] 太阳圈与虚点圈射线交点计算完成！")

if __name__ == "__main__":
    main()
